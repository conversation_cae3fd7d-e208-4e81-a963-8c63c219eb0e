import { useGetPageStories } from '@/api/project-planning';
import { TreeDataItem } from '@/components/ui/tree';
import { env } from '@/config/env';
import { useGitService } from '@/hooks/use-git-service';
import { api, axiosErrorInterpreter } from '@/lib/api-client';
import { cookie } from '@/lib/cookie';
import { useBaseStore } from '@/store/base-store';
import { useChat } from '@ai-sdk/react';
import { File, Folder } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router';
import useWebSocket from 'react-use-websocket';
import { useGetChatHistory } from './api/queries';
import { useValidateFiles } from './api/validation';
import { ChatHeader } from './components/chat-header';
import { ChatLeft } from './components/chat-left';
import { ChatRight } from './components/chat-right';
import { ChatSidebar } from './components/chat-sidebar';
import { useAutoStoryStart } from './hooks/use-auto-story-start';
import { beautifyCode } from './utils/cleanCode';
import {
  convertFileSystemTreeToGitFiles,
  convertFileSystemTreeToGitFilesFiltered,
  convertFileTreeToTreeData,
  convertGitFilesToFileSystemTree,
  convertTextToWebContainerTree,
  FileSystemTree,
  getFileContentsByPath,
  mergeFileTrees,
  modifyFilesForPreview,
} from './utils/convert';
import { getLanguage } from './utils/getLanguage';

interface UploadResponse {
  jobId: string;
  message: string;
  statusUrl: string;
  previewUrl: string;
  wsUrl: string;
}

type WebhookMessage =
  | {
      type: 'connected';
      message: string;
    }
  | {
      type: 'build-status';
      data: {
        jobId: string;
        status: 'processing' | 'completed' | 'failed'; // add more statuses if applicable
        message: string;
        progress: number;
        error?: string;
        logs?: string;
      };
    };

export default function Chat() {
  const [selectedFile, setSelectedFile] = useState({
    path: '',
    content: '',
  });
  const [treeData, setTreeData] = useState<TreeDataItem[] | TreeDataItem>([]);
  const [fileTree, setFileTree] = useState<FileSystemTree>({});
  const token = cookie.get('access_token');
  const previewRef = useRef<HTMLIFrameElement>(null);
  const [view, setView] = useState<'code' | 'preview'>('code');
  const [language, setLanguage] = useState<string>('javascript');
  const [socketUrl, setSocketUrl] = useState('');
  const [previewUrl, setPreviewUrl] = useState('');
  const [messageHistory, setMessageHistory] = useState<WebhookMessage[]>([]);
  const { lastMessage } = useWebSocket(socketUrl);
  const gitService = useGitService();
  const { moduleId, workspaceId } = useBaseStore((state) => state);
  const [searchParams] = useSearchParams();
  const pageIdFromQuery = searchParams.get('pageId');
  const getPageStories = useGetPageStories({
    workspaceId,
    moduleId: moduleId,
    pageId: pageIdFromQuery,
  });
  const pageRoute = getPageStories.data?.page.route;
  const validateFiles = useValidateFiles();

  // Fetch chat history for the current page
  const chatHistory = useGetChatHistory(workspaceId, moduleId, pageIdFromQuery);

  useEffect(() => {
    if (lastMessage !== null) {
      try {
        const parsed = JSON.parse(lastMessage.data) as WebhookMessage;
        setMessageHistory((prev) => [...prev, parsed]);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    }
  }, [lastMessage]);

  // Helper function to save files to Git repository and create a commit
  const saveFilesToGitRepository = async (
    fileTree: FileSystemTree,
    messages: { role: string; content: string; id: string }[],
  ) => {
    // Track files that were written
    const writtenFiles: string[] = [];

    // Process the file tree and write files to Git repository
    const processFileTree = async (
      tree: FileSystemTree,
      basePath: string = '',
    ) => {
      for (const [name, item] of Object.entries(tree)) {
        const path = basePath ? `${basePath}/${name}` : name;

        if ('file' in item && item.file && gitService?.writeFile) {
          const rawNewContent = item.file.contents;
          let shouldWriteFile = false;

          if (!gitService.readFile) {
            // If readFile capability doesn't exist on gitService (e.g., service not fully initialized),
            // fallback to assuming a write is needed to be safe, or handle error appropriately.
            // For now, let's assume it means we should write if we intend to change.
            shouldWriteFile = true;
          } else {
            try {
              const rawExistingContent = await gitService.readFile({
                relativeFilePath: path,
              });

              // Normalize line endings on RAW content for comparison
              const normalizedRawNew = rawNewContent
                .replace(/\r\n/g, '\n')
                .replace(/\r/g, '\n');
              const normalizedRawExisting = rawExistingContent
                .replace(/\r\n/g, '\n')
                .replace(/\r/g, '\n');

              if (normalizedRawExisting !== normalizedRawNew) {
                shouldWriteFile = true;
              }
            } catch (error) {
              // If readFile throws an error (e.g., file not found - ENOENT),
              // it's a new file, so we should write.
              console.error(error);
              shouldWriteFile = true;
            }
          }

          if (shouldWriteFile) {
            try {
              // Always write the beautified version of the new content
              const beautifiedContentToWrite = beautifyCode(
                rawNewContent,
                path,
              );
              await gitService.writeFile({
                relativeFilePath: path,
                content: beautifiedContentToWrite,
              });
              writtenFiles.push(path); // Only count if actually written
            } catch (writeError) {
              console.error(`Failed to write file: ${path}`, writeError);
            }
          }
        } else if ('directory' in item && item.directory) {
          // Process subdirectory
          await processFileTree(item.directory, path);
        }
      }
    };

    // Process the file tree
    await processFileTree(fileTree);

    // Create a commit if files were written
    if (writtenFiles.length > 0 && gitService?.createGitCommit) {
      try {
        // Extract the first line of the user's message as the commit message
        const userMessage = messages.find((msg) => msg.role === 'user');
        const userMessageContent = userMessage?.content || '';
        const commitMessage =
          userMessageContent.split('\n')[0].trim() || 'Update from chat';

        // Create the commit
        await gitService?.createGitCommit({ message: commitMessage });
      } catch {
        // Error is already logged in the createCommit function
      }
    }

    return writtenFiles;
  };

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    stop,
    status,
    append,
  } = useChat({
    api: `${env.API_URL}/chat/workspaces/${workspaceId}/modules/${moduleId}/pages/${pageIdFromQuery}`,
    initialMessages: chatHistory.data || [],
    body: {
      currentFiles: convertFileSystemTreeToGitFilesFiltered(fileTree),
    },
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    onError: (error) => {
      console.error('error', error);
      const axiosError = {
        response: {
          status: JSON.parse(error.message)?.statusCode,
          data: JSON.parse(error.message),
        },
      };
      axiosErrorInterpreter(axiosError, handleSubmit);
    },
    onFinish: async (message, { finishReason }) => {
      // Check if the response was truncated due to length limitations
      if (finishReason === 'length') {
        await append({
          role: 'user',
          content:
            'The previous response was incomplete due to length limitations. Please continue generating the response from where you left off.',
        });
        return; // Stop processing the truncated content further
      }

      try {
        // Get all assistant messages from the current conversation (stale state)
        // and add the current message to get the complete picture
        const assistantMessages = messages.filter(
          (msg) => msg.role === 'assistant',
        );

        // Add the current message to the list of assistant messages
        const allAssistantMessages = [...assistantMessages, message];

        // Combine all assistant message content to handle truncated responses
        const combinedContent = allAssistantMessages
          .map((msg) => msg.content)
          .join('\n');

        // Extract files from the combined AI response content
        const newFileTree = convertTextToWebContainerTree(combinedContent);

        // Skip if no files were generated
        if (Object.keys(newFileTree).length === 0) {
          return;
        }

        // Validate the generated code
        const filesToValidate = convertFileSystemTreeToGitFiles(newFileTree);

        const validationResult = await validateFiles.mutateAsync({
          files: filesToValidate,
        });

        if (!validationResult.isValid && validationResult.errors.length > 0) {
          await append({
            role: 'user',
            content: `The generated code has the following errors:\n ${JSON.stringify(
              validationResult.errors,
              null,
              2,
            )} \nPlease fix these errors before proceeding.`,
          });
          return; // Stop processing the truncated content further
        }

        // Merge with existing file tree
        const mergedFileTree = mergeFileTrees(fileTree, newFileTree);

        // Update the file tree state immediately
        setFileTree(mergedFileTree);

        const mergedGitFiles = convertFileSystemTreeToGitFiles(mergedFileTree);
        // Modify files for preview mode (package.json and page route)
        const modifiedGitFiles = modifyFilesForPreview(
          mergedGitFiles,
          pageRoute,
        );
        const updatedFileTree =
          convertGitFilesToFileSystemTree(modifiedGitFiles);

        try {
          // Update the preview - send the complete merged project
          const response = (await api.post('/upload', updatedFileTree, {
            baseURL: `http://localhost:${env.SERVER_PORT}`,
          })) as UploadResponse;

          const previewUrl = `${response.previewUrl}/index.html`;
          setPreviewUrl(previewUrl);
          setSocketUrl(response.wsUrl);

          setView('preview');

          // Save files to Git repository and create a commit
          await saveFilesToGitRepository(newFileTree, messages);

          // if (workspaceId) {
          //   await gitService?.pushToGitHubRepo({
          //     workspaceId,
          //     squash: true,
          //   });
          // }
        } catch (error) {
          console.error('Failed to compile React code for preview:', error);
          // Continue with Git operations even if preview fails
        }
      } catch (error) {
        console.error('Failed to process AI response or create commit:', error);
      }
    },
  });

  // Auto-resolve build failures by sending them to AI
  useEffect(() => {
    if (lastMessage !== null) {
      try {
        const parsed = JSON.parse(lastMessage.data) as WebhookMessage;

        // Auto-resolve build failures by sending them to AI
        if (
          parsed.type === 'build-status' &&
          parsed.data.status === 'failed' &&
          parsed.data.error &&
          status !== 'streaming' // Don't interrupt if AI is currently responding
        ) {
          const buildError = parsed.data.error;
          const buildLogs = parsed.data.logs;

          // Clean up the logs by removing Docker stream control characters
          const cleanLogs = buildLogs
            ? buildLogs
                // Remove Docker stream control characters by filtering out non-printable characters
                .split('')
                .filter((char) => {
                  const code = char.charCodeAt(0);
                  // Keep printable characters, newlines, and tabs
                  return (
                    (code >= 32 && code <= 126) ||
                    code === 10 ||
                    code === 13 ||
                    code === 9
                  );
                })
                .join('')
            : '';

          // Extract relevant error information from logs
          const errorLines = cleanLogs
            .split('\n')
            .filter(
              (line: string) =>
                line.includes('Failed to compile') ||
                line.includes('Module not found') ||
                line.includes("Can't resolve") ||
                line.includes('Error:') ||
                line.includes('TypeError:') ||
                line.includes('SyntaxError:') ||
                line.includes('ReferenceError:') ||
                line.includes('Build failed') ||
                line.includes('webpack errors') ||
                line.includes('Import trace'),
            )
            .slice(0, 10); // Limit to first 10 relevant error lines

          const errorContext =
            errorLines.length > 0
              ? `\n\nDetailed error information:\n${errorLines.join('\n')}`
              : '';

          // Send build failure to AI for auto-resolution
          append({
            role: 'user',
            content: `The build process failed with the following error: ${buildError}${errorContext}\n\nPlease analyze the error and fix the code to resolve this build failure. Focus on the specific modules or dependencies that couldn't be resolved.`,
          }).catch((error) => {
            console.error('Failed to send build error to AI:', error);
          });
        }
      } catch (error) {
        console.error(
          'Failed to parse WebSocket message for build error handling:',
          error,
        );
      }
    }
  }, [lastMessage, append, status]);

  // UseEffect to update the file tree and tree data to the default git files
  useEffect(() => {
    const handleSetDefaultGitFiles = async () => {
      if (
        gitService?.getAllProjectFiles &&
        Object.keys(fileTree).length === 0
      ) {
        // Only run if fileTree is empty
        const gitFiles = await gitService.getAllProjectFiles();
        // const gitFiles = baseTemplateSourceCode.files;
        if (gitFiles && Object.keys(gitFiles).length > 0) {
          const initialFileTree = convertGitFilesToFileSystemTree(gitFiles);
          setFileTree(initialFileTree);

          const initialTreeData = convertFileTreeToTreeData(
            initialFileTree,
            Folder,
            File,
          );
          setTreeData(initialTreeData);

          // Optionally, set an initial selected file
          // For example, select the first file if available
          const firstFilePath = Object.keys(gitFiles)[0];
          if (firstFilePath) {
            const firstFileContent = (gitFiles as any)[firstFilePath];
            setSelectedFile({
              path: firstFilePath,
              content: beautifyCode(firstFileContent, firstFilePath),
            });
            setLanguage(getLanguage(firstFilePath));
          }
        }
      }
    };

    handleSetDefaultGitFiles();
  }, [gitService, fileTree]); // Add fileTree to dependencies to prevent re-running if already populated

  // Auto-start logic for initializing conversation with stories
  useAutoStoryStart({
    chatHistory,
    storiesData: getPageStories,
    pageId: pageIdFromQuery,
    isEnabled: true,
    append,
    messages,
    status,
  });

  useEffect(() => {
    if (status !== 'streaming') return; // Only update when streaming
    if (messages.length === 0) return; // No messages yet
    const latestMessage = messages[messages.length - 1];

    // Convert the latest message to a file tree
    const newFileTree = convertTextToWebContainerTree(latestMessage.content);

    // Merge the new file tree with the existing one
    const mergedFileTree = mergeFileTrees(fileTree, newFileTree);

    // Generate tree data from the merged file tree
    const updatedTreeData = convertFileTreeToTreeData(
      mergedFileTree,
      Folder,
      File,
    );

    // Get the last file in the new tree to focus on it
    const lastItem =
      Array.isArray(updatedTreeData) && updatedTreeData.length > 0
        ? updatedTreeData[updatedTreeData.length - 1]
        : (updatedTreeData as TreeDataItem | null);

    const lastItemChildren = lastItem?.children;
    const lastChildIndex = lastItemChildren?.length
      ? lastItemChildren.length - 1
      : 0;

    const currentFile = lastItem
      ? lastItemChildren && lastItemChildren.length > 0
        ? lastItemChildren[lastChildIndex]
        : lastItem
      : null;

    // Try to find the file in the new tree first, then fall back to the merged tree
    let rawContent = getFileContentsByPath(
      newFileTree,
      currentFile?.path ?? '',
    );

    // If the file isn't in the new tree, look in the merged tree
    if (rawContent === null) {
      rawContent =
        getFileContentsByPath(mergedFileTree, currentFile?.path ?? '') ?? '';
    }

    setSelectedFile({
      path: currentFile?.path ?? '',
      content: beautifyCode(rawContent, currentFile?.path ?? ''),
    });
    const fileExtension = currentFile?.path;

    // Update state with the merged data
    setFileTree(mergedFileTree);
    setTreeData(updatedTreeData);
    setLanguage(getLanguage(fileExtension));
  }, [messages, fileTree, status]);

  // Show loading state while fetching chat history
  if (chatHistory.isLoading) {
    return (
      <div className="flex h-screen bg-black-200 items-center justify-center">
        <div>Loading chat history...</div>
      </div>
    );
  }

  // Check for required parameters
  if (!workspaceId || !moduleId || !pageIdFromQuery) {
    return (
      <div className="flex h-screen bg-black-200 items-center justify-center">
        <div>Missing required parameters (workspace, module, or page ID)</div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-black-200">
      <ChatSidebar />

      <div className="flex flex-col gap-0 w-full ">
        <ChatHeader />

        <div className="flex gap-2 w-full h-screen bg-black-200">
          <ChatLeft
            status={status}
            messages={messages}
            input={input}
            handleInputChange={handleInputChange}
            handleStop={stop}
            setView={setView}
            handleChatSubmit={handleSubmit}
          />

          <ChatRight
            treeData={treeData}
            fileTree={fileTree}
            setSelectedFile={setSelectedFile}
            setLanguage={setLanguage}
            previewRef={previewRef}
            language={language}
            selectedFile={selectedFile}
            view={view}
            setView={setView}
            buildMessages={messageHistory.filter(
              (msg) => msg.type === 'build-status',
            )}
            previewUrl={previewUrl}
            setPreviewUrl={setPreviewUrl}
          />
        </div>
      </div>
    </div>
  );
}
